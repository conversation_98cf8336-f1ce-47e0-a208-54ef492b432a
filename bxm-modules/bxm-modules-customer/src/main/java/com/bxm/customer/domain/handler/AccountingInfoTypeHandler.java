package com.bxm.customer.domain.handler;

import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.bxm.customer.domain.vo.valueAdded.AccountingInfoVO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.io.IOException;

/**
 * AccountingInfoVO <-> JSON字符串 的MyBatis-Plus TypeHandler
 *
 * 继承 AbstractJsonTypeHandler 是MP提供的一种更便捷的方式来处理JSON
 */
@MappedTypes(AccountingInfoVO.class)
@MappedJdbcTypes(JdbcType.VARCHAR) // 或者 JdbcType.OTHER，如果数据库列是JSON类型
public class AccountingInfoTypeHandler extends AbstractJsonTypeHandler<AccountingInfoVO> {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    protected AccountingInfoVO parse(String json) {
        try {
            return objectMapper.readValue(json, AccountingInfoVO.class);
        } catch (IOException e) {
            // 在实际项目中，这里应该记录日志并抛出自定义的运行时异常
            throw new RuntimeException("无法将JSON反序列化为AccountingInfoVO", e);
        }
    }

    @Override
    protected String toJson(AccountingInfoVO obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("无法将AccountingInfoVO序列化为JSON", e);
        }
    }
}