package com.bxm.customer.domain.vo.valueAdded;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 账务子类型枚举（非标账务下）
 */
@Getter
@AllArgsConstructor
public enum AccountSubType {

    HIGH_TECH(1, "高新账"),
    VOUCHER_BASED(2, "凭票入账"),
    SPECIAL_INDUSTRY(3, "特殊行业"),
    NON_PROFIT(4, "民非");

    @JsonValue
    private final Integer code;
    private final String description;

    @JsonCreator
    public static AccountSubType fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        return Arrays.stream(AccountSubType.values())
                .filter(type -> type.getCode().equals(code))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("无效的账务子类型代码: " + code));
    }
}